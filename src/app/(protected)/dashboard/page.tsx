"use client";

import React from 'react';
import PageHeading from '@/components/ui/PageHeading';
import AssetSummary from '@/components/Dashboard/AssetSummary';
import VaultSummary from '@/components/Dashboard/VaultSummary';
import TrusteeSummary from '@/components/Dashboard/TrusteeSummary';
import ContactSummary from '@/components/Dashboard/ContactSummary';
import ServiceSummary from '@/components/Dashboard/ServiceSummary';
import LastWishesSummary from '@/components/Dashboard/LastWishesSummary';
import TimeCapsuleSummary from '@/components/Dashboard/TimeCapsuleSummary';
import WillAdvisorSummary from '@/components/Dashboard/WillAdvisorSummary';

export default function DashboardPage() {
  return (
    <div className="max-w-7xl mx-auto">
      <PageHeading
        title="Dashboard"
        description="Manage your digital legacy in one secure place."
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {/* Core features - first row */}
        <div className="feature-card">
          <AssetSummary />
        </div>

        <div className="feature-card">
          <VaultSummary />
        </div>

        <div className="feature-card">
          <TrusteeSummary />
        </div>

        {/* Second row */}
        <div className="feature-card">
          <ContactSummary />
        </div>

        <div className="feature-card">
          <ServiceSummary />
        </div>

        <div className="feature-card">
          <LastWishesSummary />
        </div>

        {/* Third row */}
        <div className="feature-card">
          <TimeCapsuleSummary />
        </div>

        <div className="feature-card">
          <WillAdvisorSummary />
        </div>
      </div>
    </div>
  );
}
