import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import {
  createUser,
  generateVerificationCode,
  getUserByEmail,
  sendVerificationEmail,
  storeVerificationCode,
  markEmailAsVerified,
  createSession,
  createUserProfile,
  supabaseAdmin
} from '@/lib/auth-utils-edge';

// Route segment config
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    const { email, password, firstName, lastName, autoVerify = false, trusteeInvitationId, selectedPlan = 'free' } = await request.json();

    // Validate input
    if (!email || !password || !firstName || !lastName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already registered' },
        { status: 409 }
      );
    }

    // Create the user
    const user = await createUser(email, password, firstName, lastName);

    // If this is a trustee invitation registration, handle it
    if (trusteeInvitationId) {
      // Mark the email as verified
      await markEmailAsVerified(email);

      // Create a profile for the user if it doesn't exist yet
      try {
        const { data: profileData, error: profileError } = await supabaseAdmin
          .from('profiles')
          .select('id')
          .eq('id', user.id)
          .single();

        if (profileError && profileError.code === 'PGRST116') {
          // Profile doesn't exist, create it
          await createUserProfile(user.id, user.email, firstName, lastName, selectedPlan);
        }
      } catch (error) {
        console.error('Error checking/creating profile:', error);
        // Continue anyway, as this is not critical
      }

      // Create a session
      const session = await createSession(user.id);

      // Set the session cookie
      const cookieStore = await cookies();
      cookieStore.set('session_token', session.sessionToken, {
        expires: session.expiresAt,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        path: '/',
        sameSite: 'lax',
      });

      // Update the trustee record to link it to this user
      try {
        console.log(`Linking trustee invitation ${trusteeInvitationId} to user ${user.id}`);

        // First, verify the trustee record exists
        const { data: trusteeData, error: fetchError } = await supabaseAdmin
          .from('trustees')
          .select('*')
          .eq('id', trusteeInvitationId)
          .single();

        if (fetchError) {
          console.error('Error fetching trustee record:', fetchError);
          // Continue anyway, as the user account is created
        } else {
          console.log('Found trustee record:', {
            id: trusteeData.id,
            email: trusteeData.trustee_email,
            status: trusteeData.status,
            inviterId: trusteeData.user_id
          });

          // Update the trustee record to link it to the user and update names
          // The activation will happen through the TrusteeInvitationChecker component
          // after the user has accepted the terms
          const { error: trusteeError } = await supabaseAdmin
            .from('trustees')
            .update({
              trustee_user_id: user.id,
              first_name: firstName, // Update with actual first name from registration
              last_name: lastName,   // Update with actual last name from registration
              status: 'pending_auth', // Keep as pending until terms are accepted
            })
            .eq('id', trusteeInvitationId);

          if (trusteeError) {
            console.error('Error updating trustee record:', trusteeError);
            // Continue anyway, as the user account is created
          } else {
            console.log(`Successfully linked trustee invitation ${trusteeInvitationId} to user ${user.id}`);

            // Verify the update was successful
            const { data: verifyData, error: verifyError } = await supabaseAdmin
              .from('trustees')
              .select('trustee_user_id, status')
              .eq('id', trusteeInvitationId)
              .single();

            if (verifyError) {
              console.error('Error verifying trustee update:', verifyError);
            } else {
              console.log('Verified trustee update:', {
                trusteeId: trusteeInvitationId,
                linkedUserId: verifyData.trustee_user_id,
                status: verifyData.status
              });
            }
          }
        }
      } catch (trusteeUpdateError) {
        console.error('Error updating trustee record:', trusteeUpdateError);
        // Continue anyway, as the user account is created
      }

      // Return user data for auto-verified users with trustee flag
      return NextResponse.json({
        success: true,
        message: 'User created and verified successfully.',
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          emailVerified: true,
        },
        isTrusteeAccepted: true, // Flag to indicate this was a trustee acceptance
      });
    }

    // For regular sign-ups, generate and send verification code
    const verificationCode = generateVerificationCode();
    await storeVerificationCode(email, verificationCode);

    // Store the selected plan temporarily for use during verification
    if (selectedPlan === 'premium') {
      try {
        await supabaseAdmin
          .from('verification_codes')
          .update({
            metadata: JSON.stringify({ selectedPlan: 'premium' })
          })
          .eq('email', email.toLowerCase())
          .eq('code', verificationCode);
      } catch (error) {
        console.error('Error storing selected plan:', error);
        // Continue anyway, this is not critical
      }
    }

    // Send verification email
    const emailSent = await sendVerificationEmail(email, verificationCode);
    if (!emailSent) {
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'User created successfully. Please check your email for verification code.',
      userId: user.id,
    });
  } catch (error: any) {
    console.error('Error in signup:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred during signup' },
      { status: 500 }
    );
  }
}
