import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import {
  createSession,
  getUserByEmail,
  markEmailAsVerified,
  verifyCode,
  createUserProfile,
  supabaseAdmin
} from '@/lib/auth-utils-edge';

// Route segment config
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    const { email, code } = await request.json();

    // Validate input
    if (!email || !code) {
      return NextResponse.json(
        { error: 'Email and verification code are required' },
        { status: 400 }
      );
    }

    // Verify the code and get any stored metadata
    const isCodeValid = await verifyCode(email, code);
    if (!isCodeValid) {
      return NextResponse.json(
        { error: 'Invalid or expired verification code' },
        { status: 400 }
      );
    }

    // Get the selected plan from verification code metadata
    let selectedPlan: 'free' | 'premium' = 'free';
    try {
      const { data: verificationData } = await supabaseAdmin
        .from('verification_codes')
        .select('metadata')
        .eq('email', email.toLowerCase())
        .eq('code', code)
        .single();

      if (verificationData?.metadata) {
        const metadata = JSON.parse(verificationData.metadata);
        if (metadata.selectedPlan === 'premium') {
          selectedPlan = 'premium';
        }
      }
    } catch (error) {
      console.error('Error retrieving selected plan:', error);
      // Continue with default 'free' plan
    }

    // Get the user
    const user = await getUserByEmail(email);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Mark the email as verified
    await markEmailAsVerified(email);

    // Create a profile for the user if it doesn't exist yet
    try {
      const { data: profileData, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id')
        .eq('id', user.id)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it with the selected plan
        await createUserProfile(user.id, user.email, user.first_name, user.last_name, selectedPlan);
      }
    } catch (error) {
      console.error('Error checking/creating profile:', error);
      // Continue anyway, as this is not critical
    }

    // Create a session
    const session = await createSession(user.id);

    // Set the session cookie
    const cookieStore = await cookies();
    cookieStore.set('session_token', session.sessionToken, {
      expires: session.expiresAt,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      path: '/',
      sameSite: 'lax',
    });

    // We don't automatically activate trustee invitations during verification anymore
    // Instead, we'll let the TrusteeInvitationChecker component handle this
    // after the user has accepted the terms on the trustee acceptance page
    try {
      console.log(`Checking for pending trustee invitations for email: ${email}`);
      const { data: trusteeData, error: trusteeError } = await supabaseAdmin
        .from('trustees')
        .select('id, status')
        .eq('trustee_email', email.toLowerCase())
        .eq('status', 'pending_auth')
        .maybeSingle();

      if (trusteeError) {
        console.error('Error checking for pending trustee invitations:', trusteeError);
      } else if (trusteeData) {
        console.log(`Found pending trustee invitation for ${email}:`, trusteeData);
        console.log('Trustee invitation will be activated after user accepts terms');

        // We don't automatically activate the trustee invitation here
        // Instead, we'll update the status to ensure it's linked to this user
        // but not fully activated until the user accepts the terms
        const { error: updateError } = await supabaseAdmin
          .from('trustees')
          .update({
            trustee_user_id: user.id,
            // Keep status as pending_auth until terms are accepted
          })
          .eq('id', trusteeData.id);

        if (updateError) {
          console.error('Error updating trustee record:', updateError);
        } else {
          console.log(`Successfully linked trustee invitation ${trusteeData.id} to user ${user.id} (pending terms acceptance)`);
        }
      } else {
        console.log(`No pending trustee invitations found for ${email}`);
      }
    } catch (error) {
      console.error('Error handling trustee invitation during verification:', error);
      // Continue anyway, as this is not critical
    }

    // Return user data (excluding sensitive information)
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        emailVerified: true,
      },
    });
  } catch (error: any) {
    console.error('Error in verify:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred during verification' },
      { status: 500 }
    );
  }
}
