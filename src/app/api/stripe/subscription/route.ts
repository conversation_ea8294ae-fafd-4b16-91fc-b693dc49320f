import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import stripe from '@/lib/stripe';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { supabaseAdmin } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Invalid session' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Try to get the user's profile from Supabase
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    // If there's an error or no profile, return free plan
    // This handles cases where the profiles table doesn't exist yet
    if (profileError || !profile) {
      return NextResponse.json({
        plan: 'free',
        isSubscribed: false,
        subscription: null,
      });
    }

    // Check the subscription tier from the profile first
    // This handles users who signed up for premium but haven't completed Stripe payment yet
    if (profile.subscription_tier === 'premium') {
      // If they have a Stripe customer ID, check for active subscription
      if (profile.stripe_customer_id) {
        try {
          // Get the customer's subscriptions from Stripe
          const subscriptions = await stripe.subscriptions.list({
            customer: profile.stripe_customer_id,
            status: 'active',
            expand: ['data.default_payment_method'],
          });

          // If they have an active subscription, return premium with subscription details
          if (subscriptions.data.length > 0) {
            const subscription = subscriptions.data[0];
            return NextResponse.json({
              plan: 'premium',
              isSubscribed: true,
              subscription: {
                id: subscription.id,
                status: subscription.status,
                currentPeriodEnd: new Date((subscription as any).current_period_end * 1000).toISOString(),
                cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,
              },
            });
          }
        } catch (stripeError) {
          console.error('Error fetching Stripe subscription:', stripeError);
          // Continue to return premium based on profile tier
        }
      }

      // Return premium tier based on profile, even without active Stripe subscription
      return NextResponse.json({
        plan: 'premium',
        isSubscribed: false, // No active Stripe subscription
        subscription: null,
      });
    }

    // For free tier users, no need to check Stripe
    return NextResponse.json({
      plan: 'free',
      isSubscribed: false,
      subscription: null,
    });


  } catch (error: any) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while fetching subscription' },
      { status: 500 }
    );
  }
}
