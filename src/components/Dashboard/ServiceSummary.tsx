"use client";

import React, { useEffect, useState } from 'react';
import { Power, ArrowRight, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { useSubscription } from '@/context/SubscriptionContext';

interface ServiceInfo {
  count: number;
  hasServices: boolean;
}

const ServiceSummary = () => {
  const { user } = useAuth();
  const { plan, isLoading: subscriptionLoading } = useSubscription();
  const [serviceInfo, setServiceInfo] = useState<ServiceInfo>({ count: 0, hasServices: false });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchServiceInfo = async () => {
      if (!user) return;

      try {
        // Fetch service sunset entries
        const response = await fetch('/api/service-sunset');
        if (response.ok) {
          const services = await response.json();
          setServiceInfo({
            count: services.length,
            hasServices: services.length > 0
          });
        }
      } catch (error) {
        console.error('Error fetching service info:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchServiceInfo();
  }, [user]);

  if (isLoading || subscriptionLoading) {
    return (
      <div>
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center">
            <div className="feature-card-icon bg-blue-100">
              <Power className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="feature-card-title">Service Sunset</h3>
              <p className="feature-card-subtitle">Loading...</p>
            </div>
          </div>
        </div>
        <div className="feature-card-empty bg-gray-50">
          <div className="animate-pulse">
            <div className="h-8 w-8 bg-gray-300 rounded mx-auto mb-2"></div>
            <div className="h-4 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Power className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Service Sunset</h3>
            <p className="feature-card-subtitle">
              {serviceInfo.hasServices
                ? `${serviceInfo.count} service${serviceInfo.count !== 1 ? 's' : ''}`
                : 'Manage online services'
              }
            </p>
          </div>
        </div>
      </div>

      <div className="feature-card-empty bg-blue-50">
        <Power className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">
          {serviceInfo.hasServices ? 'Manage Services' : 'Track Services'}
        </h4>
        <p className="feature-card-empty-description text-blue-700">
          {serviceInfo.hasServices
            ? 'View and manage your tracked online services.'
            : 'Track online services that should be closed after your passing.'
          }
        </p>
        <Link
          href="/service-sunset"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          {serviceInfo.hasServices ? 'Manage Services' : 'Add Service'}
        </Link>
      </div>
    </div>
  );
};

export default ServiceSummary;
