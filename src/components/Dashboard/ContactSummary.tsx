"use client";

import React, { useEffect, useState } from 'react';
import { Phone, ArrowRight, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { useSubscription } from '@/context/SubscriptionContext';

interface ContactInfo {
  count: number;
  hasContacts: boolean;
}

const ContactSummary = () => {
  const { user } = useAuth();
  const { plan, isLoading: subscriptionLoading } = useSubscription();
  const [contactInfo, setContactInfo] = useState<ContactInfo>({ count: 0, hasContacts: false });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchContactInfo = async () => {
      if (!user) return;

      try {
        // Fetch contacts
        const response = await fetch('/api/contacts');
        if (response.ok) {
          const contacts = await response.json();
          setContactInfo({
            count: contacts.length,
            hasContacts: contacts.length > 0
          });
        }
      } catch (error) {
        console.error('Error fetching contact info:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchContactInfo();
  }, [user]);

  if (isLoading || subscriptionLoading) {
    return (
      <div>
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center">
            <div className="feature-card-icon bg-blue-100">
              <Phone className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="feature-card-title">Contacts</h3>
              <p className="feature-card-subtitle">Loading...</p>
            </div>
          </div>
        </div>
        <div className="feature-card-empty bg-gray-50">
          <div className="animate-pulse">
            <div className="h-8 w-8 bg-gray-300 rounded mx-auto mb-2"></div>
            <div className="h-4 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Phone className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Contacts</h3>
            <p className="feature-card-subtitle">
              {contactInfo.hasContacts
                ? `${contactInfo.count} contact${contactInfo.count !== 1 ? 's' : ''}`
                : 'Important contact information'
              }
            </p>
          </div>
        </div>
      </div>

      <div className="feature-card-empty bg-blue-50">
        <Phone className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">
          {contactInfo.hasContacts ? 'Manage Contacts' : 'Store Contacts'}
        </h4>
        <p className="feature-card-empty-description text-blue-700">
          {contactInfo.hasContacts
            ? 'View and manage your important contacts.'
            : 'Store important contact information for your trustees.'
          }
        </p>
        <Link
          href="/contacts"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          {contactInfo.hasContacts ? 'Manage Contacts' : 'Add Contact'}
        </Link>
      </div>
    </div>
  );
};

export default ContactSummary;
