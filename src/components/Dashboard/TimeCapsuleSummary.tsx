"use client";

import React, { useEffect, useState } from 'react';
import { Clock, ArrowRight, PlusCircle, Crown } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { useSubscription } from '@/context/SubscriptionContext';

interface TimeCapsuleInfo {
  count: number;
  hasTimeCapsules: boolean;
}

const TimeCapsuleSummary = () => {
  const { user } = useAuth();
  const { plan, isLoading: subscriptionLoading } = useSubscription();
  const [timeCapsuleInfo, setTimeCapsuleInfo] = useState<TimeCapsuleInfo>({ count: 0, hasTimeCapsules: false });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchTimeCapsuleInfo = async () => {
      if (!user) return;

      try {
        // Fetch time capsules
        const response = await fetch('/api/time-capsules');
        if (response.ok) {
          const timeCapsules = await response.json();
          setTimeCapsuleInfo({
            count: timeCapsules.length,
            hasTimeCapsules: timeCapsules.length > 0
          });
        }
      } catch (error) {
        console.error('Error fetching time capsule info:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTimeCapsuleInfo();
  }, [user]);

  // Show upgrade prompt for free tier users (time capsules not available on free tier)
  const showUpgradePrompt = plan === 'free';

  if (isLoading || subscriptionLoading) {
    return (
      <div>
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center">
            <div className="feature-card-icon bg-blue-100">
              <Clock className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="feature-card-title">Time Capsule</h3>
              <p className="feature-card-subtitle">Loading...</p>
            </div>
          </div>
        </div>
        <div className="feature-card-empty bg-gray-50">
          <div className="animate-pulse">
            <div className="h-8 w-8 bg-gray-300 rounded mx-auto mb-2"></div>
            <div className="h-4 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Clock className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Time Capsule</h3>
            <p className="feature-card-subtitle">
              {plan === 'premium' && timeCapsuleInfo.hasTimeCapsules
                ? `${timeCapsuleInfo.count} time capsule${timeCapsuleInfo.count !== 1 ? 's' : ''}`
                : 'Future messages for loved ones'
              }
            </p>
          </div>
        </div>
      </div>

      {showUpgradePrompt ? (
        <div className="feature-card-empty bg-amber-50 border border-amber-200">
          <Crown className="feature-card-empty-icon text-amber-500" />
          <h4 className="feature-card-empty-title text-amber-900">Premium Feature</h4>
          <p className="feature-card-empty-description text-amber-700">
            Time Capsules are available with Premium. Create up to 100 time capsules to deliver messages to your loved ones in the future.
          </p>
          <Link
            href="/pricing"
            className="feature-card-empty-button bg-amber-600 hover:bg-amber-700"
          >
            <Crown className="mr-2 h-4 w-4" />
            Upgrade to Premium
          </Link>
        </div>
      ) : (
        <div className="feature-card-empty bg-blue-50">
          <Clock className="feature-card-empty-icon text-blue-500" />
          <h4 className="feature-card-empty-title text-blue-900">
            {timeCapsuleInfo.hasTimeCapsules ? 'Manage Time Capsules' : 'Create Time Capsules'}
          </h4>
          <p className="feature-card-empty-description text-blue-700">
            {timeCapsuleInfo.hasTimeCapsules
              ? 'View and manage your scheduled time capsules.'
              : 'Create messages to be delivered to your loved ones in the future.'
            }
            <span className="block text-xs mt-1">Premium: Up to 100 time capsules</span>
          </p>
          <Link
            href="/time-capsule"
            className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
          >
            <Clock className="mr-2 h-4 w-4" />
            {timeCapsuleInfo.hasTimeCapsules ? 'Manage Time Capsules' : 'Create Time Capsule'}
          </Link>
        </div>
      )}
    </div>
  );
};

export default TimeCapsuleSummary;
