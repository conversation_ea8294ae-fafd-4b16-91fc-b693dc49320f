"use client";

import React, { useEffect, useState } from 'react';
import { Heart, ArrowRight, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { useSubscription } from '@/context/SubscriptionContext';

interface LastWishesInfo {
  hasLastWishes: boolean;
}

const LastWishesSummary = () => {
  const { user } = useAuth();
  const { plan, isLoading: subscriptionLoading } = useSubscription();
  const [lastWishesInfo, setLastWishesInfo] = useState<LastWishesInfo>({ hasLastWishes: false });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchLastWishesInfo = async () => {
      if (!user) return;

      try {
        // Fetch last wishes
        const response = await fetch('/api/last-wishes');
        if (response.ok) {
          const lastWishes = await response.json();
          setLastWishesInfo({
            hasLastWishes: lastWishes && Object.keys(lastWishes).length > 0
          });
        }
      } catch (error) {
        console.error('Error fetching last wishes info:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLastWishesInfo();
  }, [user]);

  if (isLoading || subscriptionLoading) {
    return (
      <div>
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center">
            <div className="feature-card-icon bg-blue-100">
              <Heart className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="feature-card-title">Last Wishes</h3>
              <p className="feature-card-subtitle">Loading...</p>
            </div>
          </div>
        </div>
        <div className="feature-card-empty bg-gray-50">
          <div className="animate-pulse">
            <div className="h-8 w-8 bg-gray-300 rounded mx-auto mb-2"></div>
            <div className="h-4 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Heart className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Last Wishes</h3>
            <p className="feature-card-subtitle">
              {lastWishesInfo.hasLastWishes
                ? 'Your final wishes documented'
                : 'Your final wishes and instructions'
              }
            </p>
          </div>
        </div>
      </div>

      <div className="feature-card-empty bg-blue-50">
        <Heart className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">
          {lastWishesInfo.hasLastWishes ? 'Manage Last Wishes' : 'Document Your Wishes'}
        </h4>
        <p className="feature-card-empty-description text-blue-700">
          {lastWishesInfo.hasLastWishes
            ? 'View and update your documented final wishes.'
            : 'Record your final wishes to share with your trustees.'
          }
        </p>
        <Link
          href="/last-wishes"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          {lastWishesInfo.hasLastWishes ? 'Manage Last Wishes' : 'Add Last Wishes'}
        </Link>
      </div>
    </div>
  );
};

export default LastWishesSummary;
