"use client";

import React, { useEffect, useState } from 'react';
import { Package, ArrowRight, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { useSubscription } from '@/context/SubscriptionContext';

interface AssetInfo {
  count: number;
  hasAssets: boolean;
}

const AssetSummary = () => {
  const { user } = useAuth();
  const { plan, isLoading: subscriptionLoading } = useSubscription();
  const [assetInfo, setAssetInfo] = useState<AssetInfo>({ count: 0, hasAssets: false });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchAssetInfo = async () => {
      if (!user) return;

      try {
        // Fetch assets
        const response = await fetch('/api/assets');
        if (response.ok) {
          const assets = await response.json();
          setAssetInfo({
            count: assets.length,
            hasAssets: assets.length > 0
          });
        }
      } catch (error) {
        console.error('Error fetching asset info:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssetInfo();
  }, [user]);

  if (isLoading || subscriptionLoading) {
    return (
      <div>
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center">
            <div className="feature-card-icon bg-blue-100">
              <Package className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="feature-card-title">Assets</h3>
              <p className="feature-card-subtitle">Loading...</p>
            </div>
          </div>
        </div>
        <div className="feature-card-empty bg-gray-50">
          <div className="animate-pulse">
            <div className="h-8 w-8 bg-gray-300 rounded mx-auto mb-2"></div>
            <div className="h-4 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Package className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Assets</h3>
            <p className="feature-card-subtitle">
              {assetInfo.hasAssets
                ? `${assetInfo.count} asset${assetInfo.count !== 1 ? 's' : ''}`
                : 'Track your valuable assets'
              }
            </p>
          </div>
        </div>
      </div>

      <div className="feature-card-empty bg-blue-50">
        <Package className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">
          {assetInfo.hasAssets ? 'Manage Assets' : 'Track Your Assets'}
        </h4>
        <p className="feature-card-empty-description text-blue-700">
          {assetInfo.hasAssets
            ? 'View and manage your tracked assets.'
            : 'Track your physical and digital assets for your legacy plan.'
          }
        </p>
        <Link
          href="/assets"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          {assetInfo.hasAssets ? 'Manage Assets' : 'Add Asset'}
        </Link>
      </div>
    </div>
  );
};

export default AssetSummary;
